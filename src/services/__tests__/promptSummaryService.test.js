/**
 * 提示词摘要服务测试
 */

import { promptSummaryService } from '../promptSummaryService';

// Mock LLM service
jest.mock('../llmService', () => ({
  getLLMResponse: jest.fn()
}));

// Mock Elasticsearch service
jest.mock('../elasticsearchService', () => ({
  elasticsearchService: {
    savePrompt: jest.fn()
  }
}));

import { getLLMResponse } from '../llmService';
import { elasticsearchService } from '../elasticsearchService';

describe('PromptSummaryService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // 启用 Elasticsearch 用于测试
    promptSummaryService.setElasticsearchEnabled(true);
  });

  describe('generateOriginalPromptSummary', () => {
    it('should generate summary for original prompt', async () => {
      const mockSummary = '这是一个关于创建React应用的提示词，主要目标是生成完整的项目结构和代码实现。';
      getLLMResponse.mockResolvedValue(mockSummary);

      const originalPrompt = '请帮我创建一个React应用，包含用户管理和数据展示功能。';
      const result = await promptSummaryService.generateOriginalPromptSummary(originalPrompt);

      expect(result).toBe(mockSummary);
      expect(getLLMResponse).toHaveBeenCalledWith(
        expect.stringContaining(originalPrompt),
        expect.objectContaining({
          temperature: 0.3,
          max_tokens: 1000
        })
      );
    });

    it('should handle LLM service errors', async () => {
      getLLMResponse.mockRejectedValue(new Error('LLM service error'));

      const originalPrompt = '测试提示词';
      
      await expect(
        promptSummaryService.generateOriginalPromptSummary(originalPrompt)
      ).rejects.toThrow('原始提示词摘要生成失败: LLM service error');
    });
  });

  describe('generateEnhancedPromptSummary', () => {
    it('should generate summary for enhanced prompt', async () => {
      const mockSummary = '增强后的提示词具有更完整的项目结构，包含详细的技术栈要求和实现规范。';
      getLLMResponse.mockResolvedValue(mockSummary);

      const enhancedPrompt = `# 项目开发需求
## 项目概述
创建一个现代化的React应用...`;
      
      const result = await promptSummaryService.generateEnhancedPromptSummary(enhancedPrompt);

      expect(result).toBe(mockSummary);
      expect(getLLMResponse).toHaveBeenCalledWith(
        expect.stringContaining(enhancedPrompt),
        expect.objectContaining({
          temperature: 0.3,
          max_tokens: 1200
        })
      );
    });
  });

  describe('generateDualSummaries', () => {
    it('should generate both summaries in parallel', async () => {
      const mockOriginalSummary = '原始提示词摘要';
      const mockEnhancedSummary = '增强提示词摘要';
      
      getLLMResponse
        .mockResolvedValueOnce(mockOriginalSummary)
        .mockResolvedValueOnce(mockEnhancedSummary);

      const originalPrompt = '原始提示词';
      const enhancedPrompt = '增强后的提示词';
      
      const result = await promptSummaryService.generateDualSummaries(originalPrompt, enhancedPrompt);

      expect(result).toEqual({
        originalSummary: mockOriginalSummary,
        enhancedSummary: mockEnhancedSummary,
        timestamp: expect.any(String)
      });
      expect(getLLMResponse).toHaveBeenCalledTimes(2);
    });

    it('should handle partial failures', async () => {
      getLLMResponse
        .mockRejectedValueOnce(new Error('Original summary failed'))
        .mockResolvedValueOnce('增强提示词摘要');

      const originalPrompt = '原始提示词';
      const enhancedPrompt = '增强后的提示词';
      
      await expect(
        promptSummaryService.generateDualSummaries(originalPrompt, enhancedPrompt)
      ).rejects.toThrow('双重摘要生成失败');
    });
  });

  describe('saveSummaryToElasticsearch', () => {
    it('should save summary data to Elasticsearch', async () => {
      const mockResponse = { _id: 'test-id', result: 'created' };
      elasticsearchService.savePrompt.mockResolvedValue(mockResponse);

      const summaryData = {
        originalPrompt: '原始提示词',
        enhancedPrompt: '增强提示词',
        originalSummary: '原始摘要',
        enhancedSummary: '增强摘要'
      };

      const result = await promptSummaryService.saveSummaryToElasticsearch(summaryData);

      expect(result).toBe(mockResponse);
      expect(elasticsearchService.savePrompt).toHaveBeenCalledWith(
        expect.objectContaining({
          prompt: summaryData.originalPrompt,
          enhanced_prompt: summaryData.enhancedPrompt,
          summary: summaryData.originalSummary,
          enhanced_summary: summaryData.enhancedSummary,
          metadata: expect.objectContaining({
            type: 'prompt_summary',
            language: 'zh-CN'
          })
        })
      );
    });

    it('should handle Elasticsearch errors gracefully', async () => {
      elasticsearchService.savePrompt.mockRejectedValue(new Error('ES error'));

      const summaryData = {
        originalPrompt: '原始提示词',
        enhancedPrompt: '增强提示词',
        originalSummary: '原始摘要',
        enhancedSummary: '增强摘要'
      };

      // 应该返回 null 而不是抛出错误
      const result = await promptSummaryService.saveSummaryToElasticsearch(summaryData);
      expect(result).toBeNull();
    });

    it('should skip saving when Elasticsearch is disabled', async () => {
      promptSummaryService.setElasticsearchEnabled(false);

      const summaryData = {
        originalPrompt: '原始提示词',
        enhancedPrompt: '增强提示词',
        originalSummary: '原始摘要',
        enhancedSummary: '增强摘要'
      };

      const result = await promptSummaryService.saveSummaryToElasticsearch(summaryData);

      expect(result).toBeNull();
      expect(elasticsearchService.savePrompt).not.toHaveBeenCalled();
    });
  });

  describe('processPromptSummaries', () => {
    it('should complete the full summary processing workflow', async () => {
      const mockOriginalSummary = '原始提示词摘要';
      const mockEnhancedSummary = '增强提示词摘要';
      const mockSaveResponse = { _id: 'test-id', result: 'created' };
      
      getLLMResponse
        .mockResolvedValueOnce(mockOriginalSummary)
        .mockResolvedValueOnce(mockEnhancedSummary);
      elasticsearchService.savePrompt.mockResolvedValue(mockSaveResponse);

      const originalPrompt = '原始提示词';
      const enhancedPrompt = '增强后的提示词';
      
      const result = await promptSummaryService.processPromptSummaries(originalPrompt, enhancedPrompt);

      expect(result).toEqual({
        originalSummary: mockOriginalSummary,
        enhancedSummary: mockEnhancedSummary,
        timestamp: expect.any(String),
        saved: true,
        metadata: expect.objectContaining({
          originalLength: originalPrompt.length,
          enhancedLength: enhancedPrompt.length,
          processingTime: expect.any(Number)
        })
      });
    });
  });

  describe('setElasticsearchEnabled', () => {
    it('should enable/disable Elasticsearch usage', () => {
      promptSummaryService.setElasticsearchEnabled(false);
      expect(promptSummaryService.useElasticsearch).toBe(false);

      promptSummaryService.setElasticsearchEnabled(true);
      expect(promptSummaryService.useElasticsearch).toBe(true);
    });
  });
});
