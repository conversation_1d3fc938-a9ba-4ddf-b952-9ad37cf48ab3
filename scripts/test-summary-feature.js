/**
 * 测试提示词摘要功能的独立脚本
 * 运行方式: node scripts/test-summary-feature.js
 */

// 模拟LLM服务响应
const mockLLMService = {
  getLLMResponse: async (prompt, options) => {
    console.log('🤖 模拟LLM调用...');
    console.log('📝 提示词长度:', prompt.length);
    console.log('⚙️ 配置:', options);
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    if (prompt.includes('原始提示词内容')) {
      return `这是一个关于${prompt.match(/原始提示词内容\n(.+)/)?.[1]?.substring(0, 20) || '项目开发'}的提示词。主要目标是创建完整的应用程序，包含用户界面、业务逻辑和数据管理功能。核心需求包括响应式设计、数据持久化和用户交互体验优化。`;
    } else if (prompt.includes('增强后提示词内容')) {
      return `经过AI增强的提示词具有更完整的项目结构和详细的技术规范。优化后的内容包括：标准化的目录结构、详细的功能模块划分、完善的技术栈配置、规范化的代码组织方式、以及专业的开发指导。相比原始版本，增强后的提示词提供了更具体的实现路径和质量标准。`;
    }
    
    return '模拟摘要内容生成完成。';
  }
};

// 模拟Elasticsearch服务
const mockElasticsearchService = {
  savePrompt: async (document) => {
    console.log('💾 模拟保存到Elasticsearch...');
    console.log('📄 文档ID:', document.id);
    console.log('📊 元数据:', document.metadata);
    
    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return {
      _id: document.id,
      result: 'created',
      _index: 'prompts'
    };
  }
};

// 简化版的摘要服务
class TestPromptSummaryService {
  constructor() {
    this.useElasticsearch = true;
  }

  generateId() {
    return `summary_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  async generateOriginalPromptSummary(originalPrompt, options = {}) {
    try {
      console.log('🔍 开始生成原始提示词摘要...');
      
      const summaryPrompt = `# 任务说明
你是一位专业的文档分析师，需要对用户的原始提示词进行深度分析和摘要提取。

# 原始提示词内容
${originalPrompt}

# 分析要求
请仔细分析上述原始提示词，提取并总结核心目标、关键需求、约束条件和输出格式要求。

请直接输出摘要内容，不需要额外的格式标记。`;
      
      const summary = await mockLLMService.getLLMResponse(summaryPrompt, {
        temperature: 0.3,
        max_tokens: 1000,
        ...options
      });

      console.log('✅ 原始提示词摘要生成成功');
      return summary.trim();
    } catch (error) {
      console.error('❌ 生成原始提示词摘要失败:', error);
      throw new Error(`原始提示词摘要生成失败: ${error.message}`);
    }
  }

  async generateEnhancedPromptSummary(enhancedPrompt, options = {}) {
    try {
      console.log('🔍 开始生成增强提示词摘要...');
      
      const summaryPrompt = `# 任务说明
你是一位专业的文档分析师，需要对经过AI增强优化的提示词进行深度分析和摘要提取。

# 增强后提示词内容
${enhancedPrompt}

# 分析要求
请仔细分析上述增强后的提示词，提取并总结优化后的核心目标、完善的需求体系、规范化的约束条件、标准化的输出格式和增强特色。

请直接输出摘要内容，不需要额外的格式标记。`;
      
      const summary = await mockLLMService.getLLMResponse(summaryPrompt, {
        temperature: 0.3,
        max_tokens: 1200,
        ...options
      });

      console.log('✅ 增强提示词摘要生成成功');
      return summary.trim();
    } catch (error) {
      console.error('❌ 生成增强提示词摘要失败:', error);
      throw new Error(`增强提示词摘要生成失败: ${error.message}`);
    }
  }

  async generateDualSummaries(originalPrompt, enhancedPrompt, options = {}) {
    try {
      console.log('🔄 开始生成双重摘要...');
      
      // 并行生成两个摘要以提高效率
      const [originalSummary, enhancedSummary] = await Promise.all([
        this.generateOriginalPromptSummary(originalPrompt, options),
        this.generateEnhancedPromptSummary(enhancedPrompt, options)
      ]);

      const result = {
        originalSummary,
        enhancedSummary,
        timestamp: new Date().toISOString()
      };

      console.log('✅ 双重摘要生成完成');
      return result;
    } catch (error) {
      console.error('❌ 生成双重摘要失败:', error);
      throw new Error(`双重摘要生成失败: ${error.message}`);
    }
  }

  async saveSummaryToElasticsearch(summaryData, metadata = {}) {
    if (!this.useElasticsearch) {
      console.log('⚠️ Elasticsearch未启用，跳过摘要保存');
      return null;
    }

    try {
      console.log('💾 保存摘要数据到Elasticsearch...');

      const document = {
        id: this.generateId(),
        prompt: summaryData.originalPrompt,
        enhanced_prompt: summaryData.enhancedPrompt,
        summary: summaryData.originalSummary,
        enhanced_summary: summaryData.enhancedSummary,
        timestamp: new Date().toISOString(),
        metadata: {
          originalLength: summaryData.originalPrompt.length,
          enhancedLength: summaryData.enhancedPrompt.length,
          summaryLength: summaryData.originalSummary.length,
          enhancedSummaryLength: summaryData.enhancedSummary.length,
          wordCountOriginal: summaryData.originalPrompt.split(/\s+/).length,
          wordCountEnhanced: summaryData.enhancedPrompt.split(/\s+/).length,
          language: 'zh-CN',
          type: 'prompt_summary',
          ...metadata
        }
      };

      const response = await mockElasticsearchService.savePrompt(document);
      
      console.log('✅ 摘要数据保存成功');
      return response;
    } catch (error) {
      console.error('❌ 保存摘要数据失败:', error);
      return null;
    }
  }

  async processPromptSummaries(originalPrompt, enhancedPrompt, options = {}) {
    try {
      console.log('🚀 开始完整的摘要处理流程...');
      
      // 生成双重摘要
      const summaries = await this.generateDualSummaries(originalPrompt, enhancedPrompt, options);
      
      // 准备存储数据
      const summaryData = {
        originalPrompt,
        enhancedPrompt,
        originalSummary: summaries.originalSummary,
        enhancedSummary: summaries.enhancedSummary
      };

      // 保存到Elasticsearch
      const saveResult = await this.saveSummaryToElasticsearch(summaryData, options.metadata);

      const result = {
        ...summaries,
        saved: saveResult !== null,
        metadata: {
          originalLength: originalPrompt.length,
          enhancedLength: enhancedPrompt.length,
          processingTime: Date.now()
        }
      };

      console.log('✅ 摘要处理流程完成');
      return result;
    } catch (error) {
      console.error('❌ 摘要处理流程失败:', error);
      throw error;
    }
  }
}

// 测试函数
async function testSummaryFeature() {
  console.log('🧪 开始测试提示词摘要功能...\n');

  const summaryService = new TestPromptSummaryService();

  // 测试数据
  const originalPrompt = `请帮我创建一个React应用，包含以下功能：
1. 用户注册和登录
2. 个人资料管理
3. 数据展示仪表板
4. 响应式设计
5. 数据持久化

技术要求：
- 使用React 18
- 状态管理用Redux
- UI组件库用Ant Design
- 后端API用Node.js`;

  const enhancedPrompt = `# 项目开发需求

## 项目概述
创建一个现代化的React应用程序，具备完整的用户管理和数据展示功能。

## 核心功能模块

### 1. 用户认证系统
- 用户注册（邮箱验证、密码强度检查）
- 用户登录（支持记住登录状态）
- 密码重置功能
- JWT token管理

### 2. 个人资料管理
- 用户信息编辑
- 头像上传
- 偏好设置
- 账户安全设置

### 3. 数据展示仪表板
- 实时数据图表
- 数据筛选和排序
- 导出功能
- 响应式布局

## 技术栈规范

### 前端技术
- React 18 (函数组件 + Hooks)
- Redux Toolkit (状态管理)
- Ant Design 5.x (UI组件库)
- React Router 6 (路由管理)
- Axios (HTTP客户端)

### 后端技术
- Node.js + Express
- MongoDB (数据库)
- JWT (身份验证)
- Multer (文件上传)

## 项目结构
\`\`\`
src/
├── components/     # 通用组件
├── pages/         # 页面组件
├── store/         # Redux store
├── services/      # API服务
├── utils/         # 工具函数
└── styles/        # 样式文件
\`\`\`

## 开发规范
- 使用TypeScript增强类型安全
- 遵循ESLint和Prettier代码规范
- 编写单元测试和集成测试
- 使用Git flow工作流

## 部署要求
- 支持Docker容器化部署
- 配置CI/CD流水线
- 环境变量管理
- 性能监控和日志记录`;

  try {
    // 测试完整流程
    const result = await summaryService.processPromptSummaries(
      originalPrompt,
      enhancedPrompt,
      {
        metadata: {
          source: 'test_script',
          testMode: true,
          timestamp: new Date().toISOString()
        }
      }
    );

    console.log('\n📋 测试结果:');
    console.log('=====================================');
    console.log('🔸 原始提示词摘要:');
    console.log(result.originalSummary);
    console.log('\n🔸 增强提示词摘要:');
    console.log(result.enhancedSummary);
    console.log('\n📊 处理统计:');
    console.log(`- 原始提示词长度: ${result.metadata.originalLength} 字符`);
    console.log(`- 增强提示词长度: ${result.metadata.enhancedLength} 字符`);
    console.log(`- 数据已保存: ${result.saved ? '是' : '否'}`);
    console.log(`- 处理时间戳: ${result.timestamp}`);

    console.log('\n✅ 测试完成！功能运行正常。');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  testSummaryFeature();
}

module.exports = { TestPromptSummaryService };
