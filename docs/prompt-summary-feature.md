# 提示词摘要自动生成功能

## 功能概述

新增的提示词摘要功能会在提示词增强完成后，自动调用LLM服务生成两个独立的摘要：

1. **原始提示词摘要**：提取并总结用户原始输入的核心要点、主要目标和关键需求
2. **增强提示词摘要**：提取并总结经过AI增强后的完善内容、改进点和专业指导

## 技术实现

### 核心组件

#### 1. 提示词模板 (`src/prompts/treeOutput.js`)
- `createOriginalPromptSummaryPrompt()`: 生成原始提示词摘要的LLM提示模板
- `createEnhancedPromptSummaryPrompt()`: 生成增强提示词摘要的LLM提示模板

#### 2. 摘要服务 (`src/services/promptSummaryService.js`)
- `generateOriginalPromptSummary()`: 生成原始提示词摘要
- `generateEnhancedPromptSummary()`: 生成增强提示词摘要
- `generateDualSummaries()`: 并行生成两个摘要
- `saveSummaryToElasticsearch()`: 保存摘要数据到ES
- `processPromptSummaries()`: 完整的摘要处理流程

#### 3. UI集成 (`src/components/TreeOutput.js`)
- 在提示词增强完成后自动触发摘要生成
- 异步处理，不阻塞用户界面
- 错误处理，确保摘要失败不影响主流程

### 数据存储结构

摘要数据存储在Elasticsearch的prompts索引中，包含以下字段：

```json
{
  "id": "summary_timestamp_randomid",
  "prompt": "原始提示词内容",
  "enhanced_prompt": "增强后提示词内容", 
  "summary": "原始提示词摘要",
  "enhanced_summary": "增强提示词摘要",
  "timestamp": "2025-01-XX...",
  "metadata": {
    "type": "prompt_summary",
    "originalLength": 150,
    "enhancedLength": 800,
    "summaryLength": 200,
    "enhancedSummaryLength": 300,
    "wordCountOriginal": 25,
    "wordCountEnhanced": 120,
    "language": "zh-CN",
    "source": "tree_output_enhancement",
    "userAgent": "..."
  }
}
```

## 使用流程

### 自动触发
1. 用户在TreeOutput组件中使用提示词增强功能
2. 增强完成后，系统自动调用`generateSummariesAsync()`
3. 并行生成原始和增强提示词的摘要
4. 异步保存到Elasticsearch（不阻塞UI）

### 手动调用
```javascript
import { promptSummaryService } from '../services/promptSummaryService';

// 生成双重摘要
const result = await promptSummaryService.processPromptSummaries(
  originalPrompt, 
  enhancedPrompt,
  {
    metadata: {
      source: 'manual_call',
      customField: 'value'
    }
  }
);

console.log(result.originalSummary);
console.log(result.enhancedSummary);
```

## 配置选项

### LLM参数
- **原始提示词摘要**: temperature=0.3, max_tokens=1000
- **增强提示词摘要**: temperature=0.3, max_tokens=1200

### Elasticsearch配置
```javascript
// 启用/禁用ES存储
promptSummaryService.setElasticsearchEnabled(true/false);
```

## 错误处理

### 优雅降级
- LLM调用失败：抛出明确错误信息，不影响主流程
- ES保存失败：记录日志但不抛出错误，确保UI正常运行
- 网络问题：自动重试机制（由底层LLM服务处理）

### 日志记录
```
🔄 开始异步生成摘要...
🔍 开始生成原始提示词摘要...
✅ 原始提示词摘要生成成功
🔍 开始生成增强提示词摘要...
✅ 增强提示词摘要生成成功
💾 保存摘要数据到Elasticsearch...
✅ 摘要数据保存成功
✅ 摘要处理流程完成
```

## 性能优化

### 并行处理
- 原始和增强提示词摘要并行生成，减少总耗时
- 异步保存到ES，不阻塞用户界面

### 资源管理
- 合理的token限制避免过长响应
- 错误边界确保单点失败不影响整体功能

## 测试覆盖

完整的单元测试覆盖：
- 摘要生成功能测试
- 错误处理测试  
- ES存储测试
- 配置管理测试

运行测试：
```bash
npm test -- --testPathPattern=promptSummaryService.test.js
```

## 未来扩展

### 可能的增强方向
1. **摘要质量评估**：添加摘要质量评分机制
2. **多语言支持**：支持英文等其他语言的摘要生成
3. **摘要模板定制**：允许用户自定义摘要生成模板
4. **批量处理**：支持批量生成历史提示词的摘要
5. **摘要搜索**：基于摘要内容的智能搜索功能

### API扩展
```javascript
// 未来可能的API
promptSummaryService.searchSummaries(query);
promptSummaryService.evaluateSummaryQuality(summary);
promptSummaryService.generateCustomSummary(prompt, template);
```

## 注意事项

1. **依赖关系**：功能依赖LLM服务和Elasticsearch服务正常运行
2. **性能影响**：每次增强会额外调用2次LLM API
3. **存储空间**：摘要数据会占用额外的ES存储空间
4. **网络要求**：需要稳定的网络连接进行LLM API调用

## 故障排除

### 常见问题
1. **摘要生成失败**：检查LLM服务配置和网络连接
2. **ES保存失败**：检查Elasticsearch服务状态和索引配置
3. **UI无响应**：确认异步调用正确实现，检查错误日志

### 调试方法
```javascript
// 启用详细日志
console.log('摘要服务状态:', promptSummaryService.useElasticsearch);

// 手动测试摘要生成
promptSummaryService.generateOriginalPromptSummary('测试提示词')
  .then(summary => console.log('摘要:', summary))
  .catch(error => console.error('错误:', error));
```
